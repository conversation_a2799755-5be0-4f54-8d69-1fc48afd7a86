// ==UserScript==
// @name         <PERSON><PERSON><PERSON><PERSON> Solver with <PERSON><PERSON><PERSON> Trainer
// @namespace    Hcaptcha Solver
// @version      10.4
// @description  Automatically solves <PERSON><PERSON><PERSON><PERSON> in browser
// <AUTHOR> ubeadulla (updated by GPT-4)
// @match        https://*.hcaptcha.com/*hcaptcha-challenge*
// @match        https://*.hcaptcha.com/*checkbox*
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// @run-at       document-start
// @connect      imageidentify.com
// @connect      cdnjs.cloudflare.com
// @connect      cdn.jsdelivr.net
// @connect      unpkg.com
// @connect      *.hcaptcha.com
// @require      https://unpkg.com/jimp@0.16.1/browser/lib/jimp.min.js
// @require      https://cdnjs.cloudflare.com/ajax/libs/tesseract.js/4.0.2/tesseract.min.js
// @require      https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.0.0/dist/tf.min.js
// @require      https://cdn.jsdelivr.net/npm/@tensorflow-models/coco-ssd@3.0.0/dist/coco-ssd.min.js
// @require      https://cdn.jsdelivr.net/npm/@tensorflow-models/mobilenet@2.2.0/dist/mobilenet.min.js
// ==/UserScript==

(async function () {
    'use strict';

    const ENABLE_DEFAULT_LANGUAGE = true;
    const DEFAULT_LANGUAGE = "English";
    const ENABLE_TENSORFLOW = true;
    const MATCH_IMAGES_USING_TRAINER = false;
    const MAX_SKIPS = 10;

    const CHECK_BOX = "#checkbox";
    const SUBMIT_BUTTON = ".button-submit";
    const TASK_IMAGE_BORDER = ".task-image .border";
    const IMAGE = ".task-image .image";
    const TASK_IMAGE = ".task-image";
    const PROMPT_TEXT = ".prompt-text";
    const NO_SELECTION = ".no-selection";
    const CHALLENGE_INPUT_FIELD = ".challenge-input .input-field";
    const CHALLENGE_INPUT = ".challenge-input";
    const CHALLENGE_IMAGE = ".challenge-example .image .image";
    const IMAGE_FOR_OCR = ".challenge-image .zoom-image";
    const LANGUAGE_SELECTOR = "#language-list .scroll-container .option span";

    const ARIA_CHECKED = "aria-checked";
    const ARIA_HIDDEN = "aria-hidden";

    const SENTENCE_TEXT_A = "Please click each image containing a ";
    const SENTENCE_TEXT_AN = "Please click each image containing an ";
    const LANGUAGE_FOR_OCR = "eng";

    const TRANSPORT_TYPES = ["airplane", "bicycle", "boat", "bus", "car", "motorbus", "motorcycle", "surfboard", "train", "truck", "trimaran", "seaplane", "speedboat"];
    const LIVING_ROOM_TYPES = ["bed", "book", "chair", "clock", "couch", "dining table", "potted plant", "tv"];
    const ANIMAL_TYPES = ["zebra", "cat", "dog"];
    const VALLEY = "valley";
    const VERTICAL_RIVER = "vertical river";

    let selectedImageCount = 0;
    let skipCount = 0;
    let tensorFlowModel, tensorFlowMobileNetModel, worker;
    let identifiedObjectsList = [];
    let exampleImageList = [];
    let identifyObjectsFromImagesCompleted = false;
    let currentExampleUrls = [];
    let useMobileNet = false;
    let useColourPattern = false;
    let newWordIdentified = false;
    let prevWord = "";
    let prevObject = "";

    const probabilityForObject = new Map([
        ["speedboat", 0.14],
        ["fireboat", 0.4],
        ["boathouse", 0.4],
        ["submarine", 0.5],
        ["printer", 0.05],
        ["stretcher", 0.05],
        ["rotisserie", 0.02],
        ["spatula", 0.05]
    ]);

    String.prototype.includesOneOf = function (arrayOfStrings) {
        if (!Array.isArray(arrayOfStrings)) return this.toLowerCase().includes(arrayOfStrings.toLowerCase());
        for (let str of arrayOfStrings) {
            if (str.startsWith("=") ? this.toLowerCase() === str.slice(1).toLowerCase() : this.toLowerCase().includes(str.toLowerCase())) return true;
        }
        return false;
    };

    String.prototype.equalsOneOf = function (arrayOfStrings) {
        if (!Array.isArray(arrayOfStrings)) return this.toLowerCase() === arrayOfStrings.toLowerCase();
        for (let str of arrayOfStrings) {
            if (str.startsWith("=") ? this.toLowerCase() === str.slice(1).toLowerCase() : this.toLowerCase() === str.toLowerCase()) return true;
        }
        return false;
    };

    const log = message => console.log(`[Hcaptcha Solver] ${message}`);
    const error = (message, err) => console.error(`[Hcaptcha Solver] ${message}`, err);
    const q = selector => document.querySelector(selector);
    const qAll = selector => document.querySelectorAll(selector);
    const isHidden = el => el.offsetParent === null;
    const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

    async function initializeTensorFlowModel() {
        if (!tensorFlowModel) tensorFlowModel = await cocoSsd.load();
        return tensorFlowModel;
    }

    async function initializeTensorFlowMobilenetModel() {
        if (!tensorFlowMobileNetModel) tensorFlowMobileNetModel = await mobilenet.load();
        return tensorFlowMobileNetModel;
    }

    function initializeTesseractWorker() {
        if (!worker) worker = new Tesseract.TesseractWorker();
    }

    function matchImages(imageUrl, word, i) {
        log(`Mencocokkan gambar menggunakan ImageIdentify API: ${imageUrl}`);
        GM_xmlhttpRequest({
            method: "POST",
            url: "https://www.imageidentify.com/objects/user-26a7681f-4b48-4f71-8f9f-93030898d70d/prd/urlapi/",
            headers: { "Content-Type": "application/x-www-form-urlencoded" },
            data: "image=" + encodeURIComponent(imageUrl),
            timeout: 8000,
            onload: response => clickImages(response, imageUrl, word, i),
            onerror: e => {
                if (e?.status && e.status !== 0) error("ImageIdentify API Error:", e);
                matchImagesUsingTensorFlow(imageUrl, word, i);
            },
            ontimeout: () => matchImagesUsingTensorFlow(imageUrl, word, i)
        });
    }

    function matchImagesUsingTensorFlow(imageUrl, word, i) {
        log(`Mencocokkan gambar menggunakan TensorFlow: ${imageUrl}`);
        try {
            const img = new Image();
            img.crossOrigin = "Anonymous";
            img.src = imageUrl;
            img.onload = () => {
                initializeTensorFlowModel()
                    .then(model => model.detect(img))
                    .then(predictions => {
                        for (let prediction of predictions) {
                            if (qAll(IMAGE)[i] && (qAll(IMAGE)[i].style.background).includes(imageUrl) &&
                                qAll(TASK_IMAGE_BORDER)[i].style.opacity === "0" &&
                                prediction.class.includesOneOf(word)) {
                                qAll(TASK_IMAGE)[i].click();
                                break;
                            }
                        }
                        img.removeAttribute("src");
                        selectedImageCount++;
                    })
                    .catch(err => error("TensorFlow Detection Error:", err));
            };
            img.onerror = err => error("Image Load Error:", err);
        } catch (err) {
            error("Error in matchImagesUsingTensorFlow:", err);
        }
    }

    function matchImagesUsingTensorFlowMobileNet(imageUrl, word, i) {
        log(`Mencocokkan gambar menggunakan MobileNet: ${imageUrl}`);
        try {
            const img = new Image();
            img.crossOrigin = "Anonymous";
            img.src = imageUrl;
            img.onload = () => {
                initializeTensorFlowMobilenetModel()
                    .then(model => model.classify(img))
                    .then(predictions => {
                        for (let prediction of predictions) {
                            let probability = probabilityForObject.get(prediction.className) || 0.077;
                            if (qAll(IMAGE)[i] && (qAll(IMAGE)[i].style.background).includes(imageUrl) &&
                                qAll(TASK_IMAGE_BORDER)[i].style.opacity === "0" &&
                                prediction.className.includesOneOf(word) && prediction.probability > probability) {
                                qAll(TASK_IMAGE)[i].click();
                                break;
                            }
                        }
                        img.removeAttribute("src");
                        selectedImageCount++;
                    })
                    .catch(err => error("MobileNet Classification Error:", err));
            };
            img.onerror = err => error("Image Load Error:", err);
        } catch (err) {
            error("Error in matchImagesUsingTensorFlowMobileNet:", err);
        }
    }

    function matchImageForVerticalRiver(imageUrl, word, i) {
        log(`Mencocokkan gambar untuk sungai vertikal: ${imageUrl}`);
        Jimp.read(imageUrl)
            .then(data => data.getBase64Async(Jimp.AUTO))
            .then(async src => {
                let img = new Image();
                img.src = src;
                await img.decode();

                let imageHeight = img.height;
                let imageWidth = img.width;
                let cropHeight = imageHeight - 0.03 * imageHeight;
                let buffer = Buffer.from(src.replace(/^data:image\/\w+;base64,/, ""), 'base64');

                return Jimp.read(buffer);
            })
            .then(data => data.crop(0, cropHeight, imageWidth, imageHeight).getBase64Async(Jimp.AUTO))
            .then(async src => {
                let img = new Image();
                img.src = src;
                await img.decode();

                let canvas = document.createElement("canvas");
                canvas.width = img.width;
                canvas.height = img.height;
                let ctx = canvas.getContext("2d");
                ctx.drawImage(img, 0, 0);

                let imageData = ctx.getImageData(0, 0, canvas.width, canvas.height).data;
                let count = 0;

                for (let j = 0; j < imageData.length; j += 4) {
                    if ((imageData[j] < 140 && imageData[j + 1] < 110 && imageData[j + 2] > 80 && imageData[j + 3] == 255) ||
                        (imageData[j] < 200 && imageData[j + 1] < 200 && imageData[j + 2] > 140 && imageData[j + 3] == 255)) {
                        count++;
                    }
                }

                if (count > 0.001 * (imageData.length / 4) && count < imageData.length / 8) {
                    if (qAll(IMAGE)[i] && (qAll(IMAGE)[i].style.background).includes(imageUrl) &&
                        qAll(TASK_IMAGE_BORDER)[i].style.opacity === "0") {
                        qAll(TASK_IMAGE)[i].click();
                    }
                }
                img.removeAttribute("src");
                selectedImageCount++;
            })
            .catch(error => console.error("Jimp Processing Error:", error));
    }


    function matchImagesUsingTrainer(imageUrl, word, i) {
        log(`Mencocokkan gambar menggunakan trainer: ${imageUrl}`);
        Jimp.read(imageUrl)
            .then(data => data.getBase64Async(Jimp.AUTO))
            .then(src => {
                let trainerInterval = setInterval(() => {
                    if (!qAll(IMAGE)[i] || !(qAll(IMAGE)[i].style.background).includes(imageUrl)) {
                        clearInterval(trainerInterval);
                        return;
                    }

                    if (GM_getValue(src)) {
                        if (qAll(TASK_IMAGE_BORDER)[i].style.opacity === "0" && GM_getValue(src) == word) {
                            log("Mengambil gambar dari trainer");
                            qAll(TASK_IMAGE)[i].click();
                        } else if (qAll(TASK_IMAGE_BORDER)[i].style.opacity === "1" && GM_getValue(src) != word) {
                            log("Menimpa gambar di trainer");
                            GM_setValue(src, word);
                        }
                        selectedImageCount++;
                        clearInterval(trainerInterval);
                    } else if (qAll(TASK_IMAGE_BORDER)[i].style.opacity === "1") {
                        GM_setValue(src, word);
                        selectedImageCount++;
                        clearInterval(trainerInterval);
                    }
                }, 1000); // Reduced interval
            })
            .catch(error => console.error("Jimp Processing Error:", error));
    }

    function clickImages(response, imageUrl, word, i) {
        try {
            if (response?.responseText && qAll(IMAGE)[i] && (qAll(IMAGE)[i].style.background).includes(imageUrl) &&
                qAll(TASK_IMAGE_BORDER)[i].style.opacity === "0") {

                let responseJson = JSON.parse(response.responseText);

                if ((responseJson.identify?.title && responseJson.identify.title.includesOneOf(word)) ||
                    (responseJson.identify?.entity && responseJson.identify.entity.includesOneOf(word)) ||
                    (responseJson.identify?.alternatives && Object.entries(responseJson.identify.alternatives).some(([key, value]) => value.includesOneOf(word) || key.includesOneOf(word)))) {
                    qAll(TASK_IMAGE)[i].click();
                }
                selectedImageCount++;
            } else {
                matchImagesUsingTensorFlow(imageUrl, word, i);
            }
        } catch (err) {
            matchImagesUsingTensorFlow(imageUrl, word, i);
        }
    }

    // ... (Other functions: getSynonyms, triggerEvent, triggerMouseEvent, unsure, getUrlFromString, getImageList, waitUntilImageSelection, waitForImagesToAppear, preProcessImage, postProcessImage, imageUsingOCR, convertTextToImage, convertImageToText, areExampleImageUrlsChanged, identifyObjectsFromImages, identifyObjectsFromImagesUsingMobileNet, getWordFromIdentifiedObjects, inputChallenge, identifyWordFromExamples, isObjectChanged, identifyWord, selectImagesAfterDelay - same as the previous version with logging and reduced intervals)

    if (window.location.href.includes("checkbox")) {
        log("Halaman checkbox terdeteksi.");
        const checkboxInterval = setInterval(() => {
            const checkbox = q(CHECK_BOX);
            if (checkbox) {
                if (checkbox.getAttribute(ARIA_CHECKED) === "true") {
                    clearInterval(checkboxInterval);
                } else if (!isHidden(checkbox) && checkbox.getAttribute(ARIA_CHECKED) === "false") {
                    checkbox.click();
                }
            }
        }, 2000); // Reduced interval
    } else {
        try {
            log("Memulai inisialisasi...");
            await initializeTesseractWorker();
            await initializeTensorFlowModel();
            await initializeTensorFlowMobilenetModel();
            log("Inisialisasi selesai. Memulai pemilihan gambar.");
            selectImages();
        } catch (err) {
            error("Error inisialisasi:", err);
        }
    }
})();
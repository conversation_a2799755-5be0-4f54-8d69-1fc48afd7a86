// ==UserScript==
// @name         Youtube Audio Delay with Gain and Compression (Optimized)
// @namespace    https://github.com/KBluePurple/youtube-delay
// @version      5.0
// @description  Menambahkan delay, kontrol gain, dan kompresi audio pada video YouTube dengan performa yang dioptimalkan.
// @icon         https://www.google.com/s2/favicons?sz=64&domain=youtube.com
// <AUTHOR>
// @match        https://www.youtube.com/*
// @match        https://music.youtube.com/*
// @match        https://m.youtube.com/*
// @match        https://www.youtube-nocookie.com/*
// @license      MIT
// @grant        GM_getValue
// @grant        GM_setValue
// @grant        GM_registerMenuCommand
// ==/UserScript==

(function () {
  "use strict";

  // Constants
  const DEFAULT_DELAY = 0.02;
  const DEFAULT_GAIN = 1.0;
  const MAX_GAIN = 2.0;
  const DEFAULT_THRESHOLD = -24; // dB, disesuaikan untuk keseimbangan yang lebih baik
  const DEFAULT_RATIO = 4; // Dikurangi untuk kompresi yang lebih halus
  const DEFAULT_KNEE = 25; // dB, untuk transisi yang lebih halus
  const DEFAULT_ATTACK = 0.005; // seconds, sedikit lebih lambat untuk transien yang lebih natural
  const DEFAULT_RELEASE = 0.05; // seconds, lebih lama untuk decay yang lebih natural

  // Super Stereo & Spatial Audio Settings yang lebih seimbang
  const DEFAULT_STEREO_WIDTH = 1.2; // Dikurangi dari 1.5 untuk stereo yang lebih natural
  const DEFAULT_REVERB_ROOM_SIZE = 0.2; // Dikurangi untuk reverb yang lebih subtle
  const DEFAULT_REVERB_DECAY = 0.4; // Disesuaikan untuk decay yang lebih natural
  const DEFAULT_REVERB_WET = 0.15; // Dikurangi untuk mix yang lebih seimbang
  const DEFAULT_BASS_BOOST = 1.2; // Dikurangi untuk bass yang tidak terlalu dominan
  const DEFAULT_TREBLE_BOOST = 1.1; // Dikurangi untuk treble yang lebih halus
  const DEFAULT_3D_EFFECT = 0.3; // Dikurangi untuk efek spasial yang lebih natural

  const SELECTORS = {
    VIDEO: "video.html5-main-video",
    FALLBACK_VIDEO: "video"
  };

  const RETRY_CONFIG = {
    MAX_ATTEMPTS: 10,
    INITIAL_DELAY: 100,
    MAX_DELAY: 2000
  };

  // Audio processor class for better organization and performance
  class AudioProcessor {
    constructor() {
      this.audioCtx = null;
      this.delayNode = null;
      this.gainNode = null;
      this.compressorNode = null;
      this.source = null;
      this.currentVideo = null;
      this.isInitialized = false;
      this.eventListeners = new Map();

      // Spatial Audio Nodes
      this.splitterNode = null;
      this.mergerNode = null;
      this.leftDelayNode = null;
      this.rightDelayNode = null;
      this.reverbNode = null;
      this.reverbGainNode = null;
      this.dryGainNode = null;
      this.bassFilterNode = null;
      this.trebleFilterNode = null;
      this.effect3DNode = null;

      // Load settings once
      this.settings = {
        delayTime: GM_getValue("delayTime", DEFAULT_DELAY),
        gainValue: GM_getValue("gainValue", DEFAULT_GAIN),
        threshold: GM_getValue("threshold", DEFAULT_THRESHOLD),
        ratio: GM_getValue("ratio", DEFAULT_RATIO),
        knee: GM_getValue("knee", DEFAULT_KNEE),
        attack: GM_getValue("attack", DEFAULT_ATTACK),
        release: GM_getValue("release", DEFAULT_RELEASE),
        stereoWidth: GM_getValue("stereoWidth", DEFAULT_STEREO_WIDTH),
        reverbRoomSize: GM_getValue("reverbRoomSize", DEFAULT_REVERB_ROOM_SIZE),
        reverbDecay: GM_getValue("reverbDecay", DEFAULT_REVERB_DECAY),
        reverbWet: GM_getValue("reverbWet", DEFAULT_REVERB_WET),
        bassBoost: GM_getValue("bassBoost", DEFAULT_BASS_BOOST),
        trebleBoost: GM_getValue("trebleBoost", DEFAULT_TREBLE_BOOST),
        effect3D: GM_getValue("effect3D", DEFAULT_3D_EFFECT)
      };
    }

    // Main initialization method
    async init() {
      try {
        const videoElement = await this.findVideoElement();
        if (!videoElement) {
          throw new Error('No video element found');
        }

        await this.initializeAudioNodes();
        await this.connectAudio(videoElement);
        this.setupVideoEventListeners(videoElement);
        
        console.log('Audio processor initialized successfully');
      } catch (error) {
        console.error('Failed to initialize audio processor:', error);
        throw error;
      }
    }

    // Optimized video element detection with exponential backoff
    async findVideoElement(attempt = 1) {
      const videoElement = document.querySelector(SELECTORS.VIDEO) ||
                          document.querySelector(SELECTORS.FALLBACK_VIDEO);

      if (videoElement) {
        return videoElement;
      }

      if (attempt >= RETRY_CONFIG.MAX_ATTEMPTS) {
        throw new Error("Video element not found after maximum attempts");
      }

      const delay = Math.min(
        RETRY_CONFIG.INITIAL_DELAY * Math.pow(2, attempt - 1),
        RETRY_CONFIG.MAX_DELAY
      );

      await new Promise(resolve => setTimeout(resolve, delay));
      return this.findVideoElement(attempt + 1);
    }

    // Initialize audio context and nodes
    async initAudioContext() {
      try {
        if (this.audioCtx && this.audioCtx.state !== 'closed') {
          return; // Already initialized
        }

        this.audioCtx = new (window.AudioContext || window.webkitAudioContext)();

        // Create basic audio nodes
        this.delayNode = this.audioCtx.createDelay(Math.max(this.settings.delayTime, 0.1));
        this.gainNode = this.audioCtx.createGain();
        this.compressorNode = this.audioCtx.createDynamicsCompressor();

        // Create spatial audio nodes
        this.createSpatialAudioNodes();

        // Set initial values for audio nodes
        this.updateAudioNodes();

        this.isInitialized = true;
      } catch (error) {
        console.error("Failed to initialize audio context:", error);
        throw error;
      }
    }

    async initializeAudioNodes() {
      try {
        if (!this.audioCtx) {
          this.audioCtx = new (window.AudioContext || window.webkitAudioContext)();
        }

        // Create basic audio nodes
        this.delayNode = this.audioCtx.createDelay();
        this.gainNode = this.audioCtx.createGain();
        this.compressorNode = this.audioCtx.createDynamicsCompressor();

        // Create nodes for stereo processing
        this.splitterNode = this.audioCtx.createChannelSplitter(2);
        this.mergerNode = this.audioCtx.createChannelMerger(2);
        this.leftDelayNode = this.audioCtx.createDelay();
        this.rightDelayNode = this.audioCtx.createDelay();

        // Create EQ nodes
        this.bassFilterNode = this.audioCtx.createBiquadFilter();
        this.bassFilterNode.type = 'lowshelf';
        this.bassFilterNode.frequency.value = 100; // Hz
        
        this.trebleFilterNode = this.audioCtx.createBiquadFilter();
        this.trebleFilterNode.type = 'highshelf';
        this.trebleFilterNode.frequency.value = 10000; // Hz

        // Create reverb nodes
        this.reverbNode = this.audioCtx.createConvolver();
        this.reverbGainNode = this.audioCtx.createGain();
        this.dryGainNode = this.audioCtx.createGain();

        // Create buffer for reverb
        const reverbBuffer = this.createReverbBuffer();
        this.reverbNode.buffer = reverbBuffer;

        // Initialize 3D audio
        this.effect3DNode = this.audioCtx.createPanner();
        this.effect3DNode.panningModel = 'HRTF';
        this.effect3DNode.distanceModel = 'inverse';
        this.effect3DNode.refDistance = 1;
        this.effect3DNode.maxDistance = 10000;
        this.effect3DNode.rolloffFactor = 1;
        this.effect3DNode.coneInnerAngle = 360;
        this.effect3DNode.coneOuterAngle = 0;
        this.effect3DNode.coneOuterGain = 0;

        // Set initial values from settings
        await this.updateAudioNodes();

        this.isInitialized = true;
        console.log('Audio nodes initialized successfully');
      } catch (error) {
        console.error('Error initializing audio nodes:', error);
        throw error;
      }
    }

    // Helper method untuk membuat buffer reverb
    createReverbBuffer() {
      const sampleRate = this.audioCtx.sampleRate;
      const length = sampleRate * 2; // 2 detik reverb
      const buffer = this.audioCtx.createBuffer(2, length, sampleRate);
      
      for (let channel = 0; channel < 2; channel++) {
        const channelData = buffer.getChannelData(channel);
        for (let i = 0; i < length; i++) {
          const decay = Math.exp(-i / (sampleRate * this.settings.reverbDecay));
          channelData[i] = (Math.random() * 2 - 1) * decay;
        }
      }
      
      return buffer;
    }

    // Create spatial audio processing nodes
    createSpatialAudioNodes() {
      try {
        // Channel splitter and merger for stereo processing
        this.splitterNode = this.audioCtx.createChannelSplitter(2);
        this.mergerNode = this.audioCtx.createChannelMerger(2);

        // Stereo widening delays
        this.leftDelayNode = this.audioCtx.createDelay(0.02);
        this.rightDelayNode = this.audioCtx.createDelay(0.02);

        // Reverb using convolver (simple impulse response)
        this.reverbNode = this.audioCtx.createConvolver();
        this.createReverbImpulse();

        // Reverb wet/dry mix
        this.reverbGainNode = this.audioCtx.createGain();
        this.dryGainNode = this.audioCtx.createGain();

        // EQ filters for bass and treble boost
        this.bassFilterNode = this.audioCtx.createBiquadFilter();
        this.bassFilterNode.type = 'lowshelf';
        this.bassFilterNode.frequency.value = 200;

        this.trebleFilterNode = this.audioCtx.createBiquadFilter();
        this.trebleFilterNode.type = 'highshelf';
        this.trebleFilterNode.frequency.value = 3000;

        // Stereo widener using Haas effect
        this.stereoWidenerNode = this.audioCtx.createGain();

      } catch (error) {
        console.error("Failed to create spatial audio nodes:", error);
      }
    }

    // Create reverb impulse response
    createReverbImpulse() {
      const sampleRate = this.audioCtx.sampleRate;
      const length = sampleRate * this.settings.reverbDecay;
      const impulse = this.audioCtx.createBuffer(2, length, sampleRate);

      for (let channel = 0; channel < 2; channel++) {
        const channelData = impulse.getChannelData(channel);
        for (let i = 0; i < length; i++) {
          const decay = Math.pow(1 - i / length, 2);
          channelData[i] = (Math.random() * 2 - 1) * decay * this.settings.reverbRoomSize;
        }
      }

      this.reverbNode.buffer = impulse;
    }

    // Update audio node values
    async updateAudioNodes() {
      if (!this.audioCtx || !this.delayNode || !this.gainNode || !this.compressorNode) {
        console.warn('Audio nodes not initialized');
        return;
      }

      try {
        // Update delay settings
        this.delayNode.delayTime.setValueAtTime(this.settings.delayTime, this.audioCtx.currentTime);

        // Update gain settings
        this.gainNode.gain.setValueAtTime(this.settings.gainValue, this.audioCtx.currentTime);

        // Update compressor settings
        this.compressorNode.threshold.setValueAtTime(this.settings.threshold, this.audioCtx.currentTime);
        this.compressorNode.ratio.setValueAtTime(this.settings.ratio, this.audioCtx.currentTime);
        this.compressorNode.knee.setValueAtTime(this.settings.knee, this.audioCtx.currentTime);
        this.compressorNode.attack.setValueAtTime(this.settings.attack, this.audioCtx.currentTime);
        this.compressorNode.release.setValueAtTime(this.settings.release, this.audioCtx.currentTime);

        // Update stereo width
        if (this.splitterNode && this.mergerNode) {
          // Implement stereo width control
          const width = this.settings.stereoWidth;
          this.leftDelayNode.delayTime.setValueAtTime(0.01 * width, this.audioCtx.currentTime);
          this.rightDelayNode.delayTime.setValueAtTime(0.01 * width, this.audioCtx.currentTime);
        }

        // Update reverb settings
        if (this.reverbNode) {
          // Menerapkan pengaturan reverb
          this.reverbNode.roomSize.setValueAtTime(this.settings.reverbRoomSize, this.audioCtx.currentTime);
          this.reverbNode.decay.setValueAtTime(this.settings.reverbDecay, this.audioCtx.currentTime);
          this.reverbGainNode.gain.setValueAtTime(this.settings.reverbWet, this.audioCtx.currentTime);
        }

        // Update EQ settings
        if (this.bassFilterNode && this.trebleFilterNode) {
          // Bass boost
          this.bassFilterNode.gain.setValueAtTime(
            (this.settings.bassBoost - 1) * 12, // Convert to dB
            this.audioCtx.currentTime
          );

          // Treble boost
          this.trebleFilterNode.gain.setValueAtTime(
            (this.settings.trebleBoost - 1) * 12, // Convert to dB
            this.audioCtx.currentTime
          );
        }

        // Update 3D effect
        if (this.effect3DNode) {
          this.effect3DNode.setPosition(
            Math.sin(this.settings.effect3D * Math.PI), // X position
            0, // Y position
            -Math.cos(this.settings.effect3D * Math.PI) // Z position
          );
        }

        console.log('Audio settings updated successfully');
      } catch (error) {
        console.error('Error updating audio nodes:', error);
      }
    }

    // Update spatial audio node values
    updateSpatialAudioNodes() {
      try {
        // Stereo widening effect
        const widthDelay = (this.settings.stereoWidth - 1.0) * 0.01;
        this.leftDelayNode.delayTime.value = Math.max(0, widthDelay);
        this.rightDelayNode.delayTime.value = Math.max(0, -widthDelay);

        // Reverb mix
        this.reverbGainNode.gain.value = this.settings.reverbWet;
        this.dryGainNode.gain.value = 1.0 - this.settings.reverbWet;

        // EQ settings
        this.bassFilterNode.gain.value = (this.settings.bassBoost - 1.0) * 12; // Convert to dB
        this.trebleFilterNode.gain.value = (this.settings.trebleBoost - 1.0) * 12; // Convert to dB

        // Update reverb impulse if room size changed
        if (this.reverbNode && this.settings.reverbRoomSize !== this.lastReverbRoomSize) {
          this.createReverbImpulse();
          this.lastReverbRoomSize = this.settings.reverbRoomSize;
        }
      } catch (error) {
        console.error("Failed to update spatial audio nodes:", error);
      }
    }

    // Connect audio nodes to video element
    async connectAudio(videoElement) {
      try {
        if (this.currentVideo === videoElement && this.source) {
          return; // Already connected to this video
        }

        // Disconnect previous source if exists
        if (this.source) {
          this.source.disconnect();
        }

        this.source = this.audioCtx.createMediaElementSource(videoElement);

        // Connect the complete audio processing chain
        this.connectAudioChain();

        this.currentVideo = videoElement;
        this.setupVideoEventListeners(videoElement);

        // Resume audio context if needed
        if (this.audioCtx.state === 'suspended') {
          await this.audioCtx.resume();
        }
      } catch (error) {
        console.error("Failed to connect audio:", error);
        throw error;
      }
    }

    // Connect the complete spatial audio processing chain
    connectAudioChain() {
      try {
        // Main processing chain: source -> delay -> gain -> compressor
        this.source.connect(this.delayNode);
        this.delayNode.connect(this.gainNode);
        this.gainNode.connect(this.compressorNode);

        // EQ processing (bass and treble)
        this.compressorNode.connect(this.bassFilterNode);
        this.bassFilterNode.connect(this.trebleFilterNode);

        // Split into stereo channels for spatial processing
        this.trebleFilterNode.connect(this.splitterNode);

        // Stereo widening: apply different delays to left and right channels
        this.splitterNode.connect(this.leftDelayNode, 0); // Left channel
        this.splitterNode.connect(this.rightDelayNode, 1); // Right channel

        // Dry signal path (direct)
        this.trebleFilterNode.connect(this.dryGainNode);

        // Reverb signal path
        this.trebleFilterNode.connect(this.reverbNode);
        this.reverbNode.connect(this.reverbGainNode);

        // Merge processed stereo channels
        this.leftDelayNode.connect(this.mergerNode, 0, 0);   // Left delay to left output
        this.rightDelayNode.connect(this.mergerNode, 0, 1);  // Right delay to right output

        // Mix dry signal to both channels
        this.dryGainNode.connect(this.mergerNode, 0, 0);     // Dry to left
        this.dryGainNode.connect(this.mergerNode, 0, 1);     // Dry to right

        // Mix reverb to both channels
        this.reverbGainNode.connect(this.mergerNode, 0, 0);  // Reverb to left
        this.reverbGainNode.connect(this.mergerNode, 0, 1);  // Reverb to right

        // Final output
        this.mergerNode.connect(this.audioCtx.destination);

      } catch (error) {
        console.error("Failed to connect audio chain:", error);
        // Fallback to simple connection
        this.source
          .connect(this.delayNode)
          .connect(this.gainNode)
          .connect(this.compressorNode)
          .connect(this.audioCtx.destination);
      }
    }

    // Setup event listeners for video element
    setupVideoEventListeners(videoElement) {
      // Clean up existing listeners
      this.removeVideoEventListeners();

      const listeners = {
        'ended': () => this.cleanup(),
        'pause': () => this.handleVideoPause(),
        'play': () => this.handleVideoPlay(),
        'loadstart': () => this.handleVideoChange()
      };

      Object.entries(listeners).forEach(([event, handler]) => {
        videoElement.addEventListener(event, handler);
        this.eventListeners.set(`${event}_${videoElement}`, { element: videoElement, event, handler });
      });
    }

    // Remove video event listeners
    removeVideoEventListeners() {
      this.eventListeners.forEach(({ element, event, handler }) => {
        element.removeEventListener(event, handler);
      });
      this.eventListeners.clear();
    }

    // Handle video pause
    handleVideoPause() {
      if (this.audioCtx && this.audioCtx.state === 'running') {
        this.audioCtx.suspend().catch(console.error);
      }
    }

    // Handle video play
    async handleVideoPlay() {
      if (this.audioCtx && this.audioCtx.state === 'suspended') {
        try {
          await this.audioCtx.resume();
        } catch (error) {
          console.error("Failed to resume audio context:", error);
        }
      }
    }

    // Handle video change (new video loaded)
    async handleVideoChange() {
      try {
        const videoElement = await this.findVideoElement();
        if (videoElement !== this.currentVideo) {
          await this.connectAudio(videoElement);
        }
      } catch (error) {
        console.error("Failed to handle video change:", error);
      }
    }

    // Cleanup resources
    async cleanup() {
      try {
        this.removeVideoEventListeners();

        if (this.source) {
          this.source.disconnect();
          this.source = null;
        }

        if (this.audioCtx && this.audioCtx.state !== 'closed') {
          await this.audioCtx.close();
        }

        this.audioCtx = null;
        this.delayNode = null;
        this.gainNode = null;
        this.compressorNode = null;
        this.currentVideo = null;
        this.isInitialized = false;
      } catch (error) {
        console.error("Cleanup error:", error);
      }
    }

    // Initialize the audio processor
    async init() {
      try {
        const videoElement = await this.findVideoElement();
        await this.initAudioContext();
        await this.connectAudio(videoElement);

        // Setup user interaction handler for audio context
        this.setupUserInteractionHandler();
      } catch (error) {
        console.error("Failed to initialize audio processor:", error);
      }
    }

    // Setup user interaction handler
    setupUserInteractionHandler() {
      const handleUserInteraction = async () => {
        if (this.audioCtx && this.audioCtx.state === 'suspended') {
          try {
            await this.audioCtx.resume();
          } catch (error) {
            console.error("Failed to resume audio context on user interaction:", error);
          }
        }
      };

      ['click', 'keydown', 'touchstart'].forEach(event => {
        document.addEventListener(event, handleUserInteraction, { once: true, passive: true });
      });
    }

    // Update settings with validation and non-blocking notifications
    updateSetting(key, value, validator, unit = '') {
      if (!validator(value)) {
        this.showNotification(`Nilai ${key} tidak valid`, 'error');
        return false;
      }

      this.settings[key] = value;
      GM_setValue(key, value);
      this.updateAudioNodes();
      this.showNotification(`${key} diatur ke ${value}${unit}`, 'success');
      return true;
    }

    // Non-blocking notification system
    showNotification(message, type = 'info') {
      // Create notification element
      const notification = document.createElement('div');
      notification.textContent = message;
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 6px;
        color: white;
        font-family: Arial, sans-serif;
        font-size: 14px;
        z-index: 10000;
        transition: opacity 0.3s ease;
        background-color: ${type === 'error' ? '#f44336' : type === 'success' ? '#4caf50' : '#2196f3'};
      `;

      document.body.appendChild(notification);

      // Auto remove after 3 seconds
      setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => {
          if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
          }
        }, 300);
      }, 3000);
    }

    // Validation functions
    validateDelay(value) {
      return typeof value === 'number' && value >= 0 && value <= 10;
    }

    validateGain(value) {
      return typeof value === 'number' && value >= 0 && value <= MAX_GAIN;
    }

    validateThreshold(value) {
      return typeof value === 'number' && value >= -100 && value <= 0;
    }

    validateRatio(value) {
      return typeof value === 'number' && value >= 1 && value <= 20;
    }

    validateKnee(value) {
      return typeof value === 'number' && value >= 0 && value <= 40;
    }

    validateAttack(value) {
      return typeof value === 'number' && value >= 0 && value <= 1;
    }

    validateRelease(value) {
      return typeof value === 'number' && value >= 0 && value <= 1;
    }

    // Spatial Audio Validation Functions
    validateStereoWidth(value) {
      return typeof value === 'number' && value >= 0 && value <= 3.0;
    }

    validateReverbRoomSize(value) {
      return typeof value === 'number' && value >= 0 && value <= 1.0;
    }

    validateReverbDecay(value) {
      return typeof value === 'number' && value >= 0.1 && value <= 5.0;
    }

    validateReverbWet(value) {
      return typeof value === 'number' && value >= 0 && value <= 1.0;
    }

    validateBassBoost(value) {
      return typeof value === 'number' && value >= 0 && value <= 3.0;
    }

    validateTrebleBoost(value) {
      return typeof value === 'number' && value >= 0 && value <= 3.0;
    }

    validate3DEffect(value) {
      return typeof value === 'number' && value >= 0 && value <= 1.0;
    }
  }

  // Create global audio processor instance
  const audioProcessor = new AudioProcessor();

  // === QUICK PRESETS (Most Used) ===
  GM_registerMenuCommand("🎭 Quick: Dolby-like", () => {
    audioProcessor.applyPreset('dolby');
  });

  GM_registerMenuCommand("🎪 Quick: Super Stereo", () => {
    audioProcessor.applyPreset('superStereo');
  });

  GM_registerMenuCommand("🎧 Quick: Headphone Enhanced", () => {
    audioProcessor.applyPreset('headphone');
  });

  GM_registerMenuCommand("🎵 Quick: Concert Hall", () => {
    audioProcessor.applyPreset('concertHall');
  });

  // === SPATIAL AUDIO CONTROLS ===
  GM_registerMenuCommand("🎛️ Spatial Audio Settings", () => {
    audioProcessor.showSpatialAudioDialog();
  });

  // === BASIC AUDIO CONTROLS ===
  GM_registerMenuCommand("🔧 Basic Audio Settings", () => {
    audioProcessor.showBasicAudioDialog();
  });

  // === ADVANCED CONTROLS ===
  GM_registerMenuCommand("⚙️ Advanced Settings", () => {
    audioProcessor.showAdvancedDialog();
  });

  // === UTILITY COMMANDS ===
  GM_registerMenuCommand(" Show Current Settings", () => {
    audioProcessor.showCurrentSettings();
  });

  // Add reset command
  GM_registerMenuCommand("🔄 Reset ke Default", () => {
    if (confirm("Reset semua pengaturan ke nilai default?")) {
      audioProcessor.settings = {
        delayTime: DEFAULT_DELAY,
        gainValue: DEFAULT_GAIN,
        threshold: DEFAULT_THRESHOLD,
        ratio: DEFAULT_RATIO,
        knee: DEFAULT_KNEE,
        attack: DEFAULT_ATTACK,
        release: DEFAULT_RELEASE,
        stereoWidth: DEFAULT_STEREO_WIDTH,
        reverbRoomSize: DEFAULT_REVERB_ROOM_SIZE,
        reverbDecay: DEFAULT_REVERB_DECAY,
        reverbWet: DEFAULT_REVERB_WET,
        bassBoost: DEFAULT_BASS_BOOST,
        trebleBoost: DEFAULT_TREBLE_BOOST,
        effect3D: DEFAULT_3D_EFFECT
      };

      // Save to storage
      Object.entries(audioProcessor.settings).forEach(([key, value]) => {
        GM_setValue(key, value);
      });

      audioProcessor.updateAudioNodes();
      audioProcessor.showNotification("Pengaturan direset ke default", "success");
    }
  });

  // Optimized initialization with better error handling
  async function initializeScript() {
    try {
      await audioProcessor.init();

      // Setup navigation change detection for SPA behavior
      let lastUrl = location.href;
      const observer = new MutationObserver(() => {
        if (location.href !== lastUrl) {
          lastUrl = location.href;
          // Reinitialize on navigation change
          setTimeout(() => audioProcessor.init(), 1000);
        }
      });

      observer.observe(document.body, { childList: true, subtree: true });

      // Cleanup on page unload
      window.addEventListener('beforeunload', () => {
        observer.disconnect();
        audioProcessor.cleanup();
      });

    } catch (error) {
      console.error("Failed to initialize YouTube Audio Processor:", error);
    }
  }

  // Initialize when DOM is ready
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", initializeScript);
  } else {
    initializeScript();
  }
})();

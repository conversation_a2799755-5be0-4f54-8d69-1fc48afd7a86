// ==UserScript==
// @name         Youtube Audio Delay with Gain and Compression (Optimized)
// @namespace    https://github.com/KBluePurple/youtube-delay
// @version      5.0
// @description  Menambahkan delay, kontrol gain, dan kompresi audio pada video YouTube dengan performa yang dioptimalkan.
// @icon         https://www.google.com/s2/favicons?sz=64&domain=youtube.com
// <AUTHOR>
// @match        https://www.youtube.com/*
// @match        https://music.youtube.com/*
// @match        https://m.youtube.com/*
// @match        https://www.youtube-nocookie.com/*
// @license      MIT
// @grant        GM_getValue
// @grant        GM_setValue
// @grant        GM_registerMenuCommand
// ==/UserScript==

(function () {
  "use strict";

  // Clean Audio Constants - Optimized for Quality
  const DEFAULT_DELAY = 0.015;
  const DEFAULT_GAIN = 1.2;
  const DEFAULT_THRESHOLD = -20;
  const DEFAULT_RATIO = 4;
  const DEFAULT_ATTACK = 0.003;
  const DEFAULT_RELEASE = 0.1;
  const DEFAULT_STEREO_WIDTH = 1.5;
  const DEFAULT_REVERB_WET = 0.15;
  const DEFAULT_BASS_BOOST = 1.25;
  const DEFAULT_TREBLE_BOOST = 1.2;
  const DEFAULT_PRESENCE_BOOST = 1.15;

  const SELECTORS = {
    VIDEO: "video.html5-main-video",
    FALLBACK_VIDEO: "video"
  };

  const RETRY_CONFIG = {
    MAX_ATTEMPTS: 10,
    INITIAL_DELAY: 100,
    MAX_DELAY: 2000
  };

  // Audio processor class for better organization and performance
  class AudioProcessor {
    constructor() {
      this.audioCtx = null;
      this.delayNode = null;
      this.gainNode = null;
      this.compressorNode = null;
      this.source = null;
      this.currentVideo = null;
      this.isInitialized = false;
      this.eventListeners = new Map();

      // Clean Audio Processing Nodes
      this.splitterNode = null;
      this.mergerNode = null;
      this.leftDelayNode = null;
      this.rightDelayNode = null;
      this.reverbNode = null;
      this.reverbGainNode = null;
      this.dryGainNode = null;
      this.bassFilterNode = null;
      this.trebleFilterNode = null;
      this.presenceFilterNode = null;   // Essential untuk vocal clarity

      // Streamlined settings - Essential parameters only
      this.settings = {
        delayTime: GM_getValue("delayTime", DEFAULT_DELAY),
        gainValue: GM_getValue("gainValue", DEFAULT_GAIN),
        threshold: GM_getValue("threshold", DEFAULT_THRESHOLD),
        ratio: GM_getValue("ratio", DEFAULT_RATIO),
        attack: GM_getValue("attack", DEFAULT_ATTACK),
        release: GM_getValue("release", DEFAULT_RELEASE),
        stereoWidth: GM_getValue("stereoWidth", DEFAULT_STEREO_WIDTH),
        reverbWet: GM_getValue("reverbWet", DEFAULT_REVERB_WET),
        bassBoost: GM_getValue("bassBoost", DEFAULT_BASS_BOOST),
        trebleBoost: GM_getValue("trebleBoost", DEFAULT_TREBLE_BOOST),
        presenceBoost: GM_getValue("presenceBoost", DEFAULT_PRESENCE_BOOST)
      };
    }

    // Main initialization method
    async init() {
      try {
        const videoElement = await this.findVideoElement();
        if (!videoElement) {
          throw new Error('No video element found');
        }

        await this.initializeAudioNodes();
        await this.connectAudio(videoElement);
        this.setupVideoEventListeners(videoElement);

        // Show enhanced status indicator and success notification
        this.createStatusIndicator();
        this.showNotification('🎵 Enhanced Crystal Clear Audio Activated! 🎵', 'success');

        console.log('Audio processor initialized successfully');
      } catch (error) {
        console.error('Failed to initialize audio processor:', error);
        throw error;
      }
    }

    // Optimized video element detection with exponential backoff
    async findVideoElement(attempt = 1) {
      const videoElement = document.querySelector(SELECTORS.VIDEO) ||
                          document.querySelector(SELECTORS.FALLBACK_VIDEO);

      if (videoElement) {
        return videoElement;
      }

      if (attempt >= RETRY_CONFIG.MAX_ATTEMPTS) {
        throw new Error("Video element not found after maximum attempts");
      }

      const delay = Math.min(
        RETRY_CONFIG.INITIAL_DELAY * Math.pow(2, attempt - 1),
        RETRY_CONFIG.MAX_DELAY
      );

      await new Promise(resolve => setTimeout(resolve, delay));
      return this.findVideoElement(attempt + 1);
    }

    // Initialize audio context and nodes
    async initAudioContext() {
      try {
        if (this.audioCtx && this.audioCtx.state !== 'closed') {
          return; // Already initialized
        }

        const AudioContextClass = window.AudioContext || window['webkitAudioContext'];
        this.audioCtx = new AudioContextClass();

        // Create basic audio nodes
        this.delayNode = this.audioCtx.createDelay(Math.max(this.settings.delayTime, 0.1));
        this.gainNode = this.audioCtx.createGain();
        this.compressorNode = this.audioCtx.createDynamicsCompressor();

        // Create spatial audio nodes
        this.createSpatialAudioNodes();

        // Set initial values for audio nodes
        this.updateAudioNodes();

        this.isInitialized = true;
      } catch (error) {
        console.error("Failed to initialize audio context:", error);
        throw error;
      }
    }

    async initializeAudioNodes() {
      try {
        if (!this.audioCtx) {
          const AudioContextClass = window.AudioContext || window['webkitAudioContext'];
          this.audioCtx = new AudioContextClass();
        }

        // Create basic audio nodes
        this.delayNode = this.audioCtx.createDelay();
        this.gainNode = this.audioCtx.createGain();
        this.compressorNode = this.audioCtx.createDynamicsCompressor();

        // Create nodes for stereo processing
        this.splitterNode = this.audioCtx.createChannelSplitter(2);
        this.mergerNode = this.audioCtx.createChannelMerger(2);
        this.leftDelayNode = this.audioCtx.createDelay();
        this.rightDelayNode = this.audioCtx.createDelay();

        // Clean EQ nodes - Essential frequencies only
        this.bassFilterNode = this.audioCtx.createBiquadFilter();
        this.bassFilterNode.type = 'lowshelf';
        this.bassFilterNode.frequency.value = 100; // Hz - Clean bass
        this.bassFilterNode.Q.value = 0.5; // Gentle slope

        this.presenceFilterNode = this.audioCtx.createBiquadFilter();
        this.presenceFilterNode.type = 'peaking';
        this.presenceFilterNode.frequency.value = 2500; // Hz - Vocal clarity
        this.presenceFilterNode.Q.value = 1.0; // Moderate Q untuk natural sound

        this.trebleFilterNode = this.audioCtx.createBiquadFilter();
        this.trebleFilterNode.type = 'highshelf';
        this.trebleFilterNode.frequency.value = 10000; // Hz - Clean highs
        this.trebleFilterNode.Q.value = 0.5; // Smooth response

        // Create reverb nodes
        this.reverbNode = this.audioCtx.createConvolver();
        this.reverbGainNode = this.audioCtx.createGain();
        this.dryGainNode = this.audioCtx.createGain();

        // Create simple reverb buffer
        const reverbBuffer = this.createReverbBuffer();
        this.reverbNode.buffer = reverbBuffer;

        // Set initial values from settings
        await this.updateAudioNodes();

        this.isInitialized = true;
        console.log('Audio nodes initialized successfully');
      } catch (error) {
        console.error('Error initializing audio nodes:', error);
        throw error;
      }
    }

    // Simple clean reverb buffer
    createReverbBuffer() {
      const sampleRate = this.audioCtx.sampleRate;
      const length = sampleRate * 1; // 1 second reverb
      const buffer = this.audioCtx.createBuffer(2, length, sampleRate);

      for (let channel = 0; channel < 2; channel++) {
        const channelData = buffer.getChannelData(channel);
        for (let i = 0; i < length; i++) {
          const decay = Math.exp(-i / (sampleRate * 0.5));
          channelData[i] = (Math.random() * 2 - 1) * decay * 0.2; // Gentle reverb
        }
      }

      return buffer;
    }

    // Create spatial audio processing nodes
    createSpatialAudioNodes() {
      try {
        // Channel splitter and merger for stereo processing
        this.splitterNode = this.audioCtx.createChannelSplitter(2);
        this.mergerNode = this.audioCtx.createChannelMerger(2);

        // Stereo widening delays
        this.leftDelayNode = this.audioCtx.createDelay(0.02);
        this.rightDelayNode = this.audioCtx.createDelay(0.02);

        // Reverb using convolver (simple impulse response)
        this.reverbNode = this.audioCtx.createConvolver();
        this.createReverbImpulse();

        // Reverb wet/dry mix
        this.reverbGainNode = this.audioCtx.createGain();
        this.dryGainNode = this.audioCtx.createGain();

        // EQ filters for bass and treble boost
        this.bassFilterNode = this.audioCtx.createBiquadFilter();
        this.bassFilterNode.type = 'lowshelf';
        this.bassFilterNode.frequency.value = 200;

        this.trebleFilterNode = this.audioCtx.createBiquadFilter();
        this.trebleFilterNode.type = 'highshelf';
        this.trebleFilterNode.frequency.value = 3000;

        // Stereo widener using Haas effect
        this.stereoWidenerNode = this.audioCtx.createGain();

      } catch (error) {
        console.error("Failed to create spatial audio nodes:", error);
      }
    }

    // Create reverb impulse response
    createReverbImpulse() {
      const sampleRate = this.audioCtx.sampleRate;
      const length = sampleRate * this.settings.reverbDecay;
      const impulse = this.audioCtx.createBuffer(2, length, sampleRate);

      for (let channel = 0; channel < 2; channel++) {
        const channelData = impulse.getChannelData(channel);
        for (let i = 0; i < length; i++) {
          const decay = Math.pow(1 - i / length, 2);
          channelData[i] = (Math.random() * 2 - 1) * decay * this.settings.reverbRoomSize;
        }
      }

      this.reverbNode.buffer = impulse;
    }

    // Update audio node values
    async updateAudioNodes() {
      if (!this.audioCtx || !this.delayNode || !this.gainNode || !this.compressorNode) {
        console.warn('Audio nodes not initialized');
        return;
      }

      try {
        // Update delay settings
        this.delayNode.delayTime.setValueAtTime(this.settings.delayTime, this.audioCtx.currentTime);

        // Update gain settings
        this.gainNode.gain.setValueAtTime(this.settings.gainValue, this.audioCtx.currentTime);

        // Update compressor settings
        this.compressorNode.threshold.setValueAtTime(this.settings.threshold, this.audioCtx.currentTime);
        this.compressorNode.ratio.setValueAtTime(this.settings.ratio, this.audioCtx.currentTime);
        this.compressorNode.knee.setValueAtTime(this.settings.knee, this.audioCtx.currentTime);
        this.compressorNode.attack.setValueAtTime(this.settings.attack, this.audioCtx.currentTime);
        this.compressorNode.release.setValueAtTime(this.settings.release, this.audioCtx.currentTime);

        // Update stereo width
        if (this.splitterNode && this.mergerNode) {
          // Implement stereo width control
          const width = this.settings.stereoWidth;
          this.leftDelayNode.delayTime.setValueAtTime(0.01 * width, this.audioCtx.currentTime);
          this.rightDelayNode.delayTime.setValueAtTime(0.01 * width, this.audioCtx.currentTime);
        }

        // Update reverb settings
        if (this.reverbGainNode && this.dryGainNode) {
          this.reverbGainNode.gain.setValueAtTime(this.settings.reverbWet, this.audioCtx.currentTime);
          this.dryGainNode.gain.setValueAtTime(1.0 - this.settings.reverbWet, this.audioCtx.currentTime);

          // Recreate reverb buffer if room size or decay changed
          if (this.reverbNode && (this.lastReverbRoomSize !== this.settings.reverbRoomSize ||
                                  this.lastReverbDecay !== this.settings.reverbDecay)) {
            const reverbBuffer = this.createReverbBuffer();
            this.reverbNode.buffer = reverbBuffer;
            this.lastReverbRoomSize = this.settings.reverbRoomSize;
            this.lastReverbDecay = this.settings.reverbDecay;
          }
        }

        // Update Clean EQ settings
        if (this.bassFilterNode) {
          this.bassFilterNode.gain.setValueAtTime(
            (this.settings.bassBoost - 1) * 8, // Gentle bass boost
            this.audioCtx.currentTime
          );
        }

        if (this.presenceFilterNode) {
          this.presenceFilterNode.gain.setValueAtTime(
            (this.settings.presenceBoost - 1) * 6, // Moderate presence boost
            this.audioCtx.currentTime
          );
        }

        if (this.trebleFilterNode) {
          this.trebleFilterNode.gain.setValueAtTime(
            (this.settings.trebleBoost - 1) * 8, // Clean highs
            this.audioCtx.currentTime
          );
        }

        console.log('Audio settings updated successfully');
      } catch (error) {
        console.error('Error updating audio nodes:', error);
      }
    }

    // Update spatial audio node values
    updateSpatialAudioNodes() {
      try {
        // Stereo widening effect
        const widthDelay = (this.settings.stereoWidth - 1.0) * 0.01;
        this.leftDelayNode.delayTime.value = Math.max(0, widthDelay);
        this.rightDelayNode.delayTime.value = Math.max(0, -widthDelay);

        // Reverb mix
        this.reverbGainNode.gain.value = this.settings.reverbWet;
        this.dryGainNode.gain.value = 1.0 - this.settings.reverbWet;

        // EQ settings
        this.bassFilterNode.gain.value = (this.settings.bassBoost - 1.0) * 12; // Convert to dB
        this.trebleFilterNode.gain.value = (this.settings.trebleBoost - 1.0) * 12; // Convert to dB

        // Update reverb impulse if room size changed
        if (this.reverbNode && this.settings.reverbRoomSize !== this.lastReverbRoomSize) {
          this.createReverbImpulse();
          this.lastReverbRoomSize = this.settings.reverbRoomSize;
        }
      } catch (error) {
        console.error("Failed to update spatial audio nodes:", error);
      }
    }

    // Connect audio nodes to video element
    async connectAudio(videoElement) {
      try {
        if (this.currentVideo === videoElement && this.source) {
          return; // Already connected to this video
        }

        // Disconnect previous source if exists
        if (this.source) {
          this.source.disconnect();
        }

        this.source = this.audioCtx.createMediaElementSource(videoElement);

        // Connect the complete audio processing chain
        this.connectAudioChain();

        this.currentVideo = videoElement;
        this.setupVideoEventListeners(videoElement);

        // Resume audio context if needed
        if (this.audioCtx.state === 'suspended') {
          await this.audioCtx.resume();
        }
      } catch (error) {
        console.error("Failed to connect audio:", error);
        throw error;
      }
    }

    // Connect clean and efficient audio processing chain
    connectAudioChain() {
      try {
        // Main processing chain: source -> delay -> gain -> compressor
        this.source.connect(this.delayNode);
        this.delayNode.connect(this.gainNode);
        this.gainNode.connect(this.compressorNode);

        // Clean EQ processing chain
        this.compressorNode.connect(this.bassFilterNode);
        this.bassFilterNode.connect(this.presenceFilterNode);
        this.presenceFilterNode.connect(this.trebleFilterNode);

        // Split into stereo channels for spatial processing
        this.trebleFilterNode.connect(this.splitterNode);

        // Stereo widening dengan different delays
        this.splitterNode.connect(this.leftDelayNode, 0); // Left channel
        this.splitterNode.connect(this.rightDelayNode, 1); // Right channel

        // Dry signal path
        this.trebleFilterNode.connect(this.dryGainNode);

        // Reverb signal path
        this.trebleFilterNode.connect(this.reverbNode);
        this.reverbNode.connect(this.reverbGainNode);

        // Merge processed stereo channels
        this.leftDelayNode.connect(this.mergerNode, 0, 0);   // Left delay to left output
        this.rightDelayNode.connect(this.mergerNode, 0, 1);  // Right delay to right output

        // Mix dry signal
        this.dryGainNode.connect(this.mergerNode, 0, 0);     // Dry to left
        this.dryGainNode.connect(this.mergerNode, 0, 1);     // Dry to right

        // Mix reverb
        this.reverbGainNode.connect(this.mergerNode, 0, 0);  // Reverb to left
        this.reverbGainNode.connect(this.mergerNode, 0, 1);  // Reverb to right

        // Final output
        this.mergerNode.connect(this.audioCtx.destination);

        console.log('Clean audio chain connected successfully');

      } catch (error) {
        console.error("Failed to connect audio chain:", error);
        // Simple fallback connection
        try {
          this.source
            .connect(this.delayNode)
            .connect(this.gainNode)
            .connect(this.compressorNode)
            .connect(this.bassFilterNode)
            .connect(this.trebleFilterNode)
            .connect(this.audioCtx.destination);
        } catch (fallbackError) {
          console.error("Fallback connection failed:", fallbackError);
          // Ultimate fallback
          this.source.connect(this.audioCtx.destination);
        }
      }
    }

    // Setup event listeners for video element
    setupVideoEventListeners(videoElement) {
      // Clean up existing listeners
      this.removeVideoEventListeners();

      const listeners = {
        'ended': () => this.cleanup(),
        'pause': () => this.handleVideoPause(),
        'play': () => this.handleVideoPlay(),
        'loadstart': () => this.handleVideoChange()
      };

      Object.entries(listeners).forEach(([event, handler]) => {
        videoElement.addEventListener(event, handler);
        this.eventListeners.set(`${event}_${videoElement}`, { element: videoElement, event, handler });
      });
    }

    // Remove video event listeners
    removeVideoEventListeners() {
      this.eventListeners.forEach(({ element, event, handler }) => {
        element.removeEventListener(event, handler);
      });
      this.eventListeners.clear();
    }

    // Handle video pause
    handleVideoPause() {
      if (this.audioCtx && this.audioCtx.state === 'running') {
        this.audioCtx.suspend().catch(console.error);
      }
    }

    // Handle video play
    async handleVideoPlay() {
      if (this.audioCtx && this.audioCtx.state === 'suspended') {
        try {
          await this.audioCtx.resume();
        } catch (error) {
          console.error("Failed to resume audio context:", error);
        }
      }
    }

    // Handle video change (new video loaded)
    async handleVideoChange() {
      try {
        const videoElement = await this.findVideoElement();
        if (videoElement !== this.currentVideo) {
          await this.connectAudio(videoElement);
        }
      } catch (error) {
        console.error("Failed to handle video change:", error);
      }
    }

    // Cleanup resources
    async cleanup() {
      try {
        this.removeVideoEventListeners();

        if (this.source) {
          this.source.disconnect();
          this.source = null;
        }

        if (this.audioCtx && this.audioCtx.state !== 'closed') {
          await this.audioCtx.close();
        }

        this.audioCtx = null;
        this.delayNode = null;
        this.gainNode = null;
        this.compressorNode = null;
        this.currentVideo = null;
        this.isInitialized = false;
      } catch (error) {
        console.error("Cleanup error:", error);
      }
    }



    // Setup user interaction handler
    setupUserInteractionHandler() {
      const handleUserInteraction = async () => {
        if (this.audioCtx && this.audioCtx.state === 'suspended') {
          try {
            await this.audioCtx.resume();
          } catch (error) {
            console.error("Failed to resume audio context on user interaction:", error);
          }
        }
      };

      ['click', 'keydown', 'touchstart'].forEach(event => {
        document.addEventListener(event, handleUserInteraction, { once: true, passive: true });
      });
    }

    // Update settings with validation and non-blocking notifications
    updateSetting(key, value, validator, unit = '') {
      if (!validator(value)) {
        this.showNotification(`Nilai ${key} tidak valid`, 'error');
        return false;
      }

      this.settings[key] = value;
      GM_setValue(key, value);
      this.updateAudioNodes();
      this.showNotification(`${key} diatur ke ${value}${unit}`, 'success');
      return true;
    }

    // Enhanced notification system with better styling
    showNotification(message, type = 'info') {
      // Remove existing notifications
      const existingNotifications = document.querySelectorAll('.audio-processor-notification');
      existingNotifications.forEach(notif => notif.remove());

      // Create notification element
      const notification = document.createElement('div');
      notification.className = 'audio-processor-notification';
      notification.innerHTML = `
        <div class="notification-icon">
          ${type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️'}
        </div>
        <div class="notification-message">${message}</div>
      `;

      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 16px 20px;
        border-radius: 12px;
        color: white;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        font-size: 14px;
        font-weight: 500;
        z-index: 10001;
        display: flex;
        align-items: center;
        gap: 12px;
        min-width: 280px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        animation: slideInRight 0.3s ease-out;
        background: ${type === 'error' ?
          'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)' :
          type === 'success' ?
          'linear-gradient(135deg, #4caf50 0%, #45a049 100%)' :
          'linear-gradient(135deg, #2196f3 0%, #1976d2 100%)'};
      `;

      // Add animation styles
      const style = document.createElement('style');
      style.textContent = `
        @keyframes slideInRight {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }

        @keyframes slideOutRight {
          from {
            transform: translateX(0);
            opacity: 1;
          }
          to {
            transform: translateX(100%);
            opacity: 0;
          }
        }

        .notification-icon {
          font-size: 18px;
        }

        .notification-message {
          flex: 1;
        }
      `;

      document.head.appendChild(style);
      document.body.appendChild(notification);

      // Auto remove after 4 seconds with animation
      setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease-in forwards';
        setTimeout(() => {
          if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
          }
          if (style.parentNode) {
            style.parentNode.removeChild(style);
          }
        }, 300);
      }, 4000);
    }

    // Add visual status indicator
    createStatusIndicator() {
      // Remove existing indicator
      const existing = document.getElementById('audio-processor-status');
      if (existing) existing.remove();

      const indicator = document.createElement('div');
      indicator.id = 'audio-processor-status';
      indicator.innerHTML = `
        <div class="status-icon">🎵</div>
        <div class="status-text">Audio Enhanced</div>
      `;

      indicator.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        padding: 12px 16px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 25px;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        font-size: 12px;
        font-weight: 600;
        z-index: 10000;
        display: flex;
        align-items: center;
        gap: 8px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        cursor: pointer;
        transition: all 0.3s ease;
        opacity: 0.8;
      `;

      indicator.addEventListener('mouseenter', () => {
        indicator.style.opacity = '1';
        indicator.style.transform = 'scale(1.05)';
      });

      indicator.addEventListener('mouseleave', () => {
        indicator.style.opacity = '0.8';
        indicator.style.transform = 'scale(1)';
      });

      indicator.addEventListener('click', () => {
        this.showSpatialAudioDialog();
      });

      document.body.appendChild(indicator);

      // Auto-hide after 10 seconds
      setTimeout(() => {
        if (indicator.parentNode) {
          indicator.style.opacity = '0';
          setTimeout(() => {
            if (indicator.parentNode) {
              indicator.parentNode.removeChild(indicator);
            }
          }, 300);
        }
      }, 10000);
    }

    // Validation functions
    validateDelay(value) {
      return typeof value === 'number' && value >= 0 && value <= 10;
    }

    validateGain(value) {
      return typeof value === 'number' && value >= 0 && value <= MAX_GAIN;
    }

    validateThreshold(value) {
      return typeof value === 'number' && value >= -100 && value <= 0;
    }

    validateRatio(value) {
      return typeof value === 'number' && value >= 1 && value <= 20;
    }

    validateKnee(value) {
      return typeof value === 'number' && value >= 0 && value <= 40;
    }

    validateAttack(value) {
      return typeof value === 'number' && value >= 0 && value <= 1;
    }

    validateRelease(value) {
      return typeof value === 'number' && value >= 0 && value <= 1;
    }

    // Spatial Audio Validation Functions
    validateStereoWidth(value) {
      return typeof value === 'number' && value >= 0 && value <= 3.0;
    }

    validateReverbRoomSize(value) {
      return typeof value === 'number' && value >= 0 && value <= 1.0;
    }

    validateReverbDecay(value) {
      return typeof value === 'number' && value >= 0.1 && value <= 5.0;
    }

    validateReverbWet(value) {
      return typeof value === 'number' && value >= 0 && value <= 1.0;
    }

    validateBassBoost(value) {
      return typeof value === 'number' && value >= 0 && value <= 3.0;
    }

    validateTrebleBoost(value) {
      return typeof value === 'number' && value >= 0 && value <= 3.0;
    }



    // === CLEAN PRESET SYSTEM - Optimized untuk Audio Berkualitas ===
    applyPreset(presetName) {
      const presets = {
        dolby: {
          delayTime: 0.012,
          gainValue: 1.3,
          threshold: -18,
          ratio: 5,
          attack: 0.003,
          release: 0.1,
          stereoWidth: 1.8,
          reverbWet: 0.2,
          bassBoost: 1.3,
          trebleBoost: 1.25,
          presenceBoost: 1.2
        },
        superStereo: {
          delayTime: 0.01,
          gainValue: 1.25,
          threshold: -20,
          ratio: 4,
          attack: 0.002,
          release: 0.08,
          stereoWidth: 2.2,
          reverbWet: 0.15,
          bassBoost: 1.2,
          trebleBoost: 1.3,
          presenceBoost: 1.25
        },
        headphone: {
          delayTime: 0.015,
          gainValue: 1.2,
          threshold: -22,
          ratio: 3,
          attack: 0.004,
          release: 0.12,
          stereoWidth: 1.6,
          reverbWet: 0.18,
          bassBoost: 1.35,
          trebleBoost: 1.2,
          presenceBoost: 1.15
        },
        concertHall: {
          delayTime: 0.018,
          gainValue: 1.15,
          threshold: -16,
          ratio: 6,
          attack: 0.005,
          release: 0.15,
          stereoWidth: 2.0,
          reverbWet: 0.3,
          bassBoost: 1.25,
          trebleBoost: 1.15,
          presenceBoost: 1.1
        }
      };

      const preset = presets[presetName];
      if (!preset) {
        this.showNotification(`Preset ${presetName} tidak ditemukan`, 'error');
        return;
      }

      // Apply preset settings
      Object.entries(preset).forEach(([key, value]) => {
        this.settings[key] = value;
        GM_setValue(key, value);
      });

      this.updateAudioNodes();
      this.showNotification(`Preset ${presetName} diterapkan`, 'success');
    }

    // === MODERN UI DIALOGS ===
    createModernDialog(title, content) {
      // Remove existing dialog if any
      const existingDialog = document.getElementById('audio-processor-dialog');
      if (existingDialog) {
        existingDialog.remove();
      }

      const dialog = document.createElement('div');
      dialog.id = 'audio-processor-dialog';
      dialog.innerHTML = `
        <div class="dialog-overlay">
          <div class="dialog-container">
            <div class="dialog-header">
              <h3>${title}</h3>
              <button class="dialog-close">×</button>
            </div>
            <div class="dialog-content">
              ${content}
            </div>
          </div>
        </div>
      `;

      // Add modern styling
      const style = document.createElement('style');
      style.textContent = `
        #audio-processor-dialog {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: 10000;
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .dialog-overlay {
          background: rgba(0, 0, 0, 0.7);
          backdrop-filter: blur(5px);
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          animation: fadeIn 0.3s ease;
        }

        .dialog-container {
          background: linear-gradient(135deg, #1e1e1e 0%, #2d2d2d 100%);
          border-radius: 12px;
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
          max-width: 500px;
          width: 90%;
          max-height: 80vh;
          overflow: hidden;
          animation: slideIn 0.3s ease;
        }

        .dialog-header {
          background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
          color: white;
          padding: 20px;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .dialog-header h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
        }

        .dialog-close {
          background: none;
          border: none;
          color: white;
          font-size: 24px;
          cursor: pointer;
          padding: 0;
          width: 30px;
          height: 30px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: background 0.2s;
        }

        .dialog-close:hover {
          background: rgba(255, 255, 255, 0.2);
        }

        .dialog-content {
          padding: 20px;
          color: #e0e0e0;
          max-height: 60vh;
          overflow-y: auto;
        }

        .setting-group {
          margin-bottom: 20px;
          padding: 15px;
          background: rgba(255, 255, 255, 0.05);
          border-radius: 8px;
          border-left: 4px solid #ff6b6b;
        }

        .setting-group h4 {
          margin: 0 0 15px 0;
          color: #ff6b6b;
          font-size: 16px;
        }

        .setting-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;
        }

        .setting-item:last-child {
          margin-bottom: 0;
        }

        .setting-label {
          flex: 1;
          margin-right: 15px;
          font-size: 14px;
        }

        .setting-control {
          display: flex;
          align-items: center;
          gap: 10px;
        }

        .setting-slider {
          width: 120px;
          height: 6px;
          border-radius: 3px;
          background: #444;
          outline: none;
          -webkit-appearance: none;
        }

        .setting-slider::-webkit-slider-thumb {
          -webkit-appearance: none;
          width: 18px;
          height: 18px;
          border-radius: 50%;
          background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
          cursor: pointer;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .setting-value {
          min-width: 50px;
          text-align: center;
          font-size: 12px;
          color: #ff6b6b;
          font-weight: 600;
        }

        .preset-buttons {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
          gap: 10px;
          margin-top: 20px;
        }

        .preset-btn {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          border: none;
          padding: 12px 16px;
          border-radius: 8px;
          cursor: pointer;
          font-size: 14px;
          font-weight: 500;
          transition: all 0.2s;
        }

        .preset-btn:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }

        @keyframes slideIn {
          from { transform: translateY(-50px) scale(0.9); opacity: 0; }
          to { transform: translateY(0) scale(1); opacity: 1; }
        }
      `;

      document.head.appendChild(style);
      document.body.appendChild(dialog);

      // Event listeners
      dialog.querySelector('.dialog-close').onclick = () => {
        dialog.remove();
        style.remove();
      };

      dialog.querySelector('.dialog-overlay').onclick = (e) => {
        if (e.target === e.currentTarget) {
          dialog.remove();
          style.remove();
        }
      };

      return dialog;
    }

    // === DIALOG METHODS ===
    showSpatialAudioDialog() {
      const content = `
        <div class="setting-group">
          <h4>🎭 Stereo Enhancement</h4>
          <div class="setting-item">
            <span class="setting-label">Stereo Width</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="stereoWidth" min="1" max="2.5" step="0.1" value="${this.settings.stereoWidth}">
              <span class="setting-value" id="stereoWidthValue">${this.settings.stereoWidth}x</span>
            </div>
          </div>
          <div class="setting-item">
            <span class="setting-label">Reverb Amount</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="reverbWet" min="0" max="0.5" step="0.05" value="${this.settings.reverbWet}">
              <span class="setting-value" id="reverbWetValue">${this.settings.reverbWet}</span>
            </div>
          </div>
        </div>

        <div class="setting-group">
          <h4>🎚️ EQ Controls</h4>
          <div class="setting-item">
            <span class="setting-label">Bass</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="bassBoost" min="0.8" max="2" step="0.1" value="${this.settings.bassBoost}">
              <span class="setting-value" id="bassBoostValue">${this.settings.bassBoost}x</span>
            </div>
          </div>
          <div class="setting-item">
            <span class="setting-label">Presence</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="presenceBoost" min="0.8" max="2" step="0.1" value="${this.settings.presenceBoost}">
              <span class="setting-value" id="presenceBoostValue">${this.settings.presenceBoost}x</span>
            </div>
          </div>
          <div class="setting-item">
            <span class="setting-label">Treble</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="trebleBoost" min="0.8" max="2" step="0.1" value="${this.settings.trebleBoost}">
              <span class="setting-value" id="trebleBoostValue">${this.settings.trebleBoost}x</span>
            </div>
          </div>
        </div>

        <div class="preset-buttons">
          <button class="preset-btn" onclick="audioProcessor.applyPreset('dolby')">🎭 Dolby Enhanced</button>
          <button class="preset-btn" onclick="audioProcessor.applyPreset('superStereo')">🎪 Super Stereo</button>
          <button class="preset-btn" onclick="audioProcessor.applyPreset('headphone')">🎧 Headphone</button>
          <button class="preset-btn" onclick="audioProcessor.applyPreset('concertHall')">🎵 Concert Hall</button>
        </div>
      `;

      const dialog = this.createModernDialog('🎛️ Audio Enhancement', content);
      this.setupSliderListeners(dialog);
    }

    showBasicAudioDialog() {
      const content = `
        <div class="setting-group">
          <h4>🔊 Basic Controls</h4>
          <div class="setting-item">
            <span class="setting-label">Audio Gain</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="gainValue" min="0.8" max="2" step="0.1" value="${this.settings.gainValue}">
              <span class="setting-value" id="gainValueValue">${this.settings.gainValue}x</span>
            </div>
          </div>
          <div class="setting-item">
            <span class="setting-label">Audio Delay</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="delayTime" min="0.005" max="0.05" step="0.005" value="${this.settings.delayTime}">
              <span class="setting-value" id="delayTimeValue">${this.settings.delayTime}s</span>
            </div>
          </div>
        </div>
      `;

      const dialog = this.createModernDialog('🔧 Basic Audio Settings', content);
      this.setupSliderListeners(dialog);
    }

    showAdvancedDialog() {
      const content = `
        <div class="setting-group">
          <h4>🎛️ Compressor Settings</h4>
          <div class="setting-item">
            <span class="setting-label">Threshold</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="threshold" min="-30" max="-10" step="1" value="${this.settings.threshold}">
              <span class="setting-value" id="thresholdValue">${this.settings.threshold}dB</span>
            </div>
          </div>
          <div class="setting-item">
            <span class="setting-label">Ratio</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="ratio" min="2" max="8" step="0.5" value="${this.settings.ratio}">
              <span class="setting-value" id="ratioValue">${this.settings.ratio}:1</span>
            </div>
          </div>
          <div class="setting-item">
            <span class="setting-label">Attack</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="attack" min="0.001" max="0.01" step="0.001" value="${this.settings.attack}">
              <span class="setting-value" id="attackValue">${this.settings.attack}s</span>
            </div>
          </div>
          <div class="setting-item">
            <span class="setting-label">Release</span>
            <div class="setting-control">
              <input type="range" class="setting-slider" id="release" min="0.05" max="0.2" step="0.01" value="${this.settings.release}">
              <span class="setting-value" id="releaseValue">${this.settings.release}s</span>
            </div>
          </div>
        </div>
      `;

      const dialog = this.createModernDialog('⚙️ Compressor Settings', content);
      this.setupSliderListeners(dialog);
    }

    showCurrentSettings() {
      const content = `
        <div class="setting-group">
          <h4>📊 Current Audio Settings</h4>
          <div style="font-family: monospace; font-size: 12px; line-height: 1.6;">
            <strong>🎛️ Basic:</strong><br>
            • Delay: ${this.settings.delayTime}s<br>
            • Gain: ${this.settings.gainValue}x<br><br>

            <strong>🎚️ Compressor:</strong><br>
            • Threshold: ${this.settings.threshold}dB<br>
            • Ratio: ${this.settings.ratio}:1<br>
            • Attack: ${this.settings.attack}s<br>
            • Release: ${this.settings.release}s<br><br>

            <strong>🎭 Spatial:</strong><br>
            • Stereo Width: ${this.settings.stereoWidth}x<br>
            • Reverb: ${this.settings.reverbWet}<br><br>

            <strong>🎵 EQ:</strong><br>
            • Bass: ${this.settings.bassBoost}x<br>
            • Presence: ${this.settings.presenceBoost}x<br>
            • Treble: ${this.settings.trebleBoost}x<br>
          </div>
        </div>
      `;

      this.createModernDialog('📋 Audio Status', content);
    }

    setupSliderListeners(dialog) {
      const sliders = dialog.querySelectorAll('.setting-slider');
      sliders.forEach(slider => {
        const valueDisplay = dialog.querySelector(`#${slider.id}Value`);

        slider.addEventListener('input', (e) => {
          const value = parseFloat(e.target.value);
          const key = e.target.id;

          // Update display
          let displayValue = value;
          if (key === 'delayTime' || key === 'attack' || key === 'release') {
            displayValue = value + 's';
          } else if (key === 'threshold') {
            displayValue = value + 'dB';
          } else if (key === 'ratio') {
            displayValue = value + ':1';
          } else if (key === 'gainValue' || key === 'bassBoost' || key === 'trebleBoost' || key === 'presenceBoost') {
            displayValue = value + 'x';
          } else if (key === 'reverbWet') {
            displayValue = value.toFixed(2);
          } else if (key === 'stereoWidth') {
            displayValue = value + 'x';
          }

          valueDisplay.textContent = displayValue;

          // Update setting
          this.settings[key] = value;
          GM_setValue(key, value);
          this.updateAudioNodes();
        });
      });
    }
  }

  // Create global audio processor instance
  const audioProcessor = new AudioProcessor();

  // === STREAMLINED MENU - Essential Controls Only ===

  // Quick Presets (Most Used)
  GM_registerMenuCommand("🎭 Dolby Enhanced", () => {
    audioProcessor.applyPreset('dolby');
  });

  GM_registerMenuCommand("🎪 Super Stereo", () => {
    audioProcessor.applyPreset('superStereo');
  });

  GM_registerMenuCommand("🎧 Headphone Mode", () => {
    audioProcessor.applyPreset('headphone');
  });

  GM_registerMenuCommand("🎵 Concert Hall", () => {
    audioProcessor.applyPreset('concertHall');
  });

  // Separator
  GM_registerMenuCommand("─────────────────", () => {});

  // Settings Panels
  GM_registerMenuCommand("🎛️ Audio Enhancement", () => {
    audioProcessor.showSpatialAudioDialog();
  });

  GM_registerMenuCommand("🔧 Basic Controls", () => {
    audioProcessor.showBasicAudioDialog();
  });

  GM_registerMenuCommand("⚙️ Compressor", () => {
    audioProcessor.showAdvancedDialog();
  });

  // Separator
  GM_registerMenuCommand("─────────────────", () => {});

  // Utility
  GM_registerMenuCommand("📋 Current Settings", () => {
    audioProcessor.showCurrentSettings();
  });

  // Reset command
  GM_registerMenuCommand("🔄 Reset to Default", () => {
    if (confirm("Reset semua pengaturan ke default?")) {
      audioProcessor.settings = {
        delayTime: DEFAULT_DELAY,
        gainValue: DEFAULT_GAIN,
        threshold: DEFAULT_THRESHOLD,
        ratio: DEFAULT_RATIO,
        attack: DEFAULT_ATTACK,
        release: DEFAULT_RELEASE,
        stereoWidth: DEFAULT_STEREO_WIDTH,
        reverbWet: DEFAULT_REVERB_WET,
        bassBoost: DEFAULT_BASS_BOOST,
        trebleBoost: DEFAULT_TREBLE_BOOST,
        presenceBoost: DEFAULT_PRESENCE_BOOST
      };

      // Save to storage
      Object.entries(audioProcessor.settings).forEach(([key, value]) => {
        GM_setValue(key, value);
      });

      audioProcessor.updateAudioNodes();
      audioProcessor.showNotification("Pengaturan direset ke default", "success");
    }
  });

  // Optimized initialization with better error handling
  async function initializeScript() {
    try {
      await audioProcessor.init();

      // Setup navigation change detection for SPA behavior
      let lastUrl = location.href;
      const observer = new MutationObserver(() => {
        if (location.href !== lastUrl) {
          lastUrl = location.href;
          // Reinitialize on navigation change
          setTimeout(() => audioProcessor.init(), 1000);
        }
      });

      observer.observe(document.body, { childList: true, subtree: true });

      // Cleanup on page unload
      window.addEventListener('beforeunload', () => {
        observer.disconnect();
        audioProcessor.cleanup();
      });

    } catch (error) {
      console.error("Failed to initialize YouTube Audio Processor:", error);
    }
  }

  // Initialize when DOM is ready
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", initializeScript);
  } else {
    initializeScript();
  }
})();
